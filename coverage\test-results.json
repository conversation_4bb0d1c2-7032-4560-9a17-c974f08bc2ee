{"numTotalTestSuites": 11, "numPassedTestSuites": 11, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 19, "numPassedTests": 19, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1751302507641, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["buildUrl function with different MODE values", "MODE: development"], "fullName": "buildUrl function with different MODE values MODE: development should use proxy endpoint for videos in development mode", "status": "passed", "title": "should use proxy endpoint for videos in development mode", "duration": 5.044100000000071, "failureMessages": [], "location": {"line": 61, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "MODE: development"], "fullName": "buildUrl function with different MODE values MODE: development should use proxy endpoint for channels in development mode", "status": "passed", "title": "should use proxy endpoint for channels in development mode", "duration": 3.301400000000285, "failureMessages": [], "location": {"line": 73, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "MODE: development"], "fullName": "buildUrl function with different MODE values MODE: development should work with different localhost ports", "status": "passed", "title": "should work with different localhost ports", "duration": 2.4051999999992404, "failureMessages": [], "location": {"line": 85, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "MODE: production"], "fullName": "buildUrl function with different MODE values MODE: production should use direct Google API endpoint for videos in production mode", "status": "passed", "title": "should use direct Google API endpoint for videos in production mode", "duration": 3.6272999999991953, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "MODE: production"], "fullName": "buildUrl function with different MODE values MODE: production should use direct Google API endpoint for channels in production mode", "status": "passed", "title": "should use direct Google API endpoint for channels in production mode", "duration": 3.993399999999383, "failureMessages": [], "location": {"line": 127, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "MODE: test"], "fullName": "buildUrl function with different MODE values MODE: test should use direct Google API endpoint in test mode", "status": "passed", "title": "should use direct Google API endpoint in test mode", "duration": 5.898000000000138, "failureMessages": [], "location": {"line": 152, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "MODE: staging"], "fullName": "buildUrl function with different MODE values MODE: staging should use direct Google API endpoint for custom build modes", "status": "passed", "title": "should use direct Google API endpoint for custom build modes", "duration": 15.575800000000527, "failureMessages": [], "location": {"line": 176, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "parameter handling"], "fullName": "buildUrl function with different MODE values parameter handling should properly encode URL parameters", "status": "passed", "title": "should properly encode URL parameters", "duration": 6.130500000000211, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "parameter handling"], "fullName": "buildUrl function with different MODE values parameter handling should skip empty parameter values", "status": "passed", "title": "should skip empty parameter values", "duration": 7.186700000000201, "failureMessages": [], "location": {"line": 214, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "parameter handling"], "fullName": "buildUrl function with different MODE values parameter handling should handle multiple video IDs correctly", "status": "passed", "title": "should handle multiple video IDs correctly", "duration": 4.634299999999712, "failureMessages": [], "location": {"line": 229, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "parameter handling"], "fullName": "buildUrl function with different MODE values parameter handling should always add API key parameter", "status": "passed", "title": "should always add API key parameter", "duration": 3.0533000000004904, "failureMessages": [], "location": {"line": 238, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "missing API key handling"], "fullName": "buildUrl function with different MODE values missing API key handling should handle missing API key gracefully", "status": "passed", "title": "should handle missing API key gracefully", "duration": 2.878899999999703, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "URL structure validation"], "fullName": "buildUrl function with different MODE values URL structure validation should generate valid URLs that can be parsed", "status": "passed", "title": "should generate valid URLs that can be parsed", "duration": 4.50380000000041, "failureMessages": [], "location": {"line": 287, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "URL structure validation"], "fullName": "buildUrl function with different MODE values URL structure validation should maintain parameter order and formatting", "status": "passed", "title": "should maintain parameter order and formatting", "duration": 2.769800000000032, "failureMessages": [], "location": {"line": 302, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "edge cases"], "fullName": "buildUrl function with different MODE values edge cases should handle empty endpoint", "status": "passed", "title": "should handle empty endpoint", "duration": 2.5994999999993524, "failureMessages": [], "location": {"line": 331, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "edge cases"], "fullName": "buildUrl function with different MODE values edge cases should handle empty parameters object", "status": "passed", "title": "should handle empty parameters object", "duration": 4.742600000000493, "failureMessages": [], "location": {"line": 340, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "edge cases"], "fullName": "buildUrl function with different MODE values edge cases should handle special characters in endpoint", "status": "passed", "title": "should handle special characters in endpoint", "duration": 3.1369999999997162, "failureMessages": [], "location": {"line": 347, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "regression prevention"], "fullName": "buildUrl function with different MODE values regression prevention should maintain consistent URL format across different modes", "status": "passed", "title": "should maintain consistent URL format across different modes", "duration": 2.9893999999994776, "failureMessages": [], "location": {"line": 357, "column": 5}, "meta": {}}, {"ancestorTitles": ["buildUrl function with different MODE values", "regression prevention"], "fullName": "buildUrl function with different MODE values regression prevention should preserve API key security in all modes", "status": "passed", "title": "should preserve API key security in all modes", "duration": 4.345399999999245, "failureMessages": [], "location": {"line": 392, "column": 5}, "meta": {}}], "startTime": 1751302511342, "endTime": 1751302511433.3455, "status": "passed", "message": "", "name": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/__tests__/buildUrl.test.ts"}]}