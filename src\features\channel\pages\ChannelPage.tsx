import type React from 'react';

import { useParams } from 'react-router-dom';

/**
 * ChannelPage component for displaying a channel's content and information
 */
const ChannelPage: React.FC = () => {
  const { channelIdOrName } = useParams<{ channelIdOrName: string }>();

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Channel Page</h1>
      <p className="text-gray-600 mb-4">
        {channelIdOrName ? `Viewing channel: ${channelIdOrName}` : 'No channel specified'}
      </p>
      <div className="p-4 bg-blue-50 rounded-lg border border-blue-200 text-blue-700">
        <p>This is a placeholder for the channel page that will display a channel's videos, playlists, and information.</p>
        <p className="mt-2">For demonstration of video components, please visit the <strong>Video Demo</strong> page using the user menu dropdown.</p>
      </div>
    </div>
  );
};

export default ChannelPage;
