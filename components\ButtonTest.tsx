import React, { useEffect } from 'react';

const ButtonTest: React.FC = () => {
  useEffect(() => {
    // Global click listener to test if clicks are being detected
    const handleGlobalClick = (e: MouseEvent) => {
      console.log('Global click detected:', e.target);
    };

    document.addEventListener('click', handleGlobalClick);
    
    return () => {
      document.removeEventListener('click', handleGlobalClick);
    };
  }, []);

  const handleClick = (buttonType: string) => {
    console.log(`${buttonType} clicked!`);
    alert(`${buttonType} clicked!`);
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f0f0f0', margin: '20px', zIndex: 1000, position: 'relative' }}>
      <h2>Button Click Test Component</h2>
      
      {/* Standard HTML button */}
      <button 
        onClick={() => handleClick('HTML Button')}
        style={{
          padding: '10px 20px',
          margin: '10px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          zIndex: 1001,
          position: 'relative'
        }}
      >
        HTML Button
      </button>

      {/* React button with inline styles */}
      <button 
        onClick={() => handleClick('React Button')}
        className="bg-green-500 text-white px-4 py-2 m-2 rounded cursor-pointer"
        style={{ zIndex: 1001, position: 'relative' }}
      >
        React Button (Tailwind)
      </button>

      {/* React button with Tailwind classes */}
      <div 
        onClick={() => handleClick('Div Click')}
        className="inline-block bg-red-500 text-white px-4 py-2 m-2 rounded cursor-pointer"
        style={{ zIndex: 1001, position: 'relative' }}
      >
        Div as Button
      </div>

      {/* Test form button */}
      <form onSubmit={(e) => { e.preventDefault(); handleClick('Form Button'); }}>
        <button 
          type="submit" 
          className="bg-purple-500 text-white px-4 py-2 m-2 rounded"
          style={{ zIndex: 1001, position: 'relative' }}
        >
          Form Submit Button
        </button>
      </form>

      <div className="mt-4">
        <p>If you can't click these buttons, check the browser console for global click events.</p>
        <ul>
          <li>CSS pointer-events: none</li>
          <li>Invisible overlay blocking clicks</li>
          <li>JavaScript event handling issues</li>
          <li>React event delegation problems</li>
        </ul>
      </div>
    </div>
  );
};

export default ButtonTest;
