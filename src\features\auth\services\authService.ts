import type { AuthTokens, LoginCredentials, RegisterData, User } from '../types';

/**
 * Service for handling authentication-related API requests
 */
class AuthService {
  private baseUrl: string = '/api/auth';
  private tokenKey: string = 'auth_tokens';

  /**
   * Log in with credentials
   */
  async login(credentials: LoginCredentials): Promise<User> {
    const response = await fetch(`${this.baseUrl}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to login');
    }

    const data = await response.json();
    this.setTokens(data.tokens);
    return data.user;
  }

  /**
   * Register a new user
   */
  async register(data: RegisterData): Promise<User> {
    const response = await fetch(`${this.baseUrl}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to register');
    }

    const responseData = await response.json();
    this.setTokens(responseData.tokens);
    return responseData.user;
  }

  /**
   * Log out the current user
   */
  async logout(): Promise<void> {
    const tokens = this.getTokens();

    if (tokens?.refreshToken) {
      try {
        await fetch(`${this.baseUrl}/logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${tokens.accessToken}`,
          },
          body: JSON.stringify({ refreshToken: tokens.refreshToken }),
        });
      } catch (error) {
        console.error('Error during logout:', error);
      }
    }

    this.clearTokens();
  }

  /**
   * Get the current user
   */
  async getCurrentUser(): Promise<User | null> {
    const tokens = this.getTokens();

    if (!tokens?.accessToken) {
      return null;
    }

    try {
      const response = await fetch(`${this.baseUrl}/me`, {
        headers: {
          'Authorization': `Bearer ${tokens.accessToken}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token expired, try to refresh
          const newTokens = await this.refreshToken(tokens.refreshToken);
          if (newTokens) {
            return this.getCurrentUser();
          }
          return null;
        }
        throw new Error('Failed to get user data');
      }

      return response.json();
    } catch (error) {
      console.error('Error fetching current user:', error);
      return null;
    }
  }

  /**
   * Refresh the access token
   */
  private async refreshToken(refreshToken: string): Promise<AuthTokens | null> {
    try {
      const response = await fetch(`${this.baseUrl}/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        this.clearTokens();
        return null;
      }

      const tokens = await response.json();
      this.setTokens(tokens);
      return tokens;
    } catch (error) {
      console.error('Error refreshing token:', error);
      this.clearTokens();
      return null;
    }
  }

  /**
   * Set authentication tokens in localStorage
   */
  private setTokens(tokens: AuthTokens): void {
    localStorage.setItem(this.tokenKey, JSON.stringify(tokens));
  }

  /**
   * Get authentication tokens from localStorage
   */
  private getTokens(): AuthTokens | null {
    const tokensString = localStorage.getItem(this.tokenKey);
    if (!tokensString) {
      return null;
    }

    try {
      return JSON.parse(tokensString) as AuthTokens;
    } catch (error) {
      console.error('Error parsing auth tokens:', error);
      return null;
    }
  }

  /**
   * Clear authentication tokens from localStorage
   */
  private clearTokens(): void {
    localStorage.removeItem(this.tokenKey);
  }

  /**
   * Check if the current session is authenticated
   */
  isAuthenticated(): boolean {
    const tokens = this.getTokens();
    if (!tokens) {
      return false;
    }

    // Check if token is expired
    return tokens.expiresAt > Date.now();
  }
}

// Export as singleton
export const authService = new AuthService();
