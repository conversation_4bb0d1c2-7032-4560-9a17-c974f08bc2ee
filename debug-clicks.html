<!DOCTYPE html>
<html>
<head>
    <title>Debug Click Issues</title>
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            font-family: Arial, sans-serif; 
        }
        .test-button {
            padding: 10px 20px;
            margin: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .overlay-test {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 0, 0, 0.1);
            z-index: 9999;
            pointer-events: none;
            display: none;
        }
        .results {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Click Test Page</h1>
    <p>Test if buttons are clickable here vs in the React app:</p>
    
    <button class="test-button" onclick="handleClick('Button 1')">Test Button 1</button>
    <button class="test-button" onclick="handleClick('Button 2')">Test Button 2</button>
    <button class="test-button" onclick="toggleOverlay()">Toggle Red Overlay (simulates blocking)</button>
    
    <div id="overlay" class="overlay-test"></div>
    
    <div class="results">
        <h3>Click Results:</h3>
        <div id="results"></div>
    </div>

    <script>
        function handleClick(buttonName) {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<p>[${timestamp}] ${buttonName} clicked successfully!</p>`;
        }

        function toggleOverlay() {
            const overlay = document.getElementById('overlay');
            if (overlay.style.display === 'none' || overlay.style.display === '') {
                overlay.style.display = 'block';
                overlay.style.pointerEvents = 'auto';
                handleClick('Overlay enabled - buttons should be blocked now');
            } else {
                overlay.style.display = 'none';
                overlay.style.pointerEvents = 'none';
                handleClick('Overlay disabled - buttons should work now');
            }
        }

        // Test for existing overlays
        function detectOverlays() {
            const allElements = document.querySelectorAll('*');
            const overlays = [];
            
            allElements.forEach(el => {
                const style = window.getComputedStyle(el);
                if (style.position === 'fixed' || style.position === 'absolute') {
                    if (parseInt(style.zIndex) > 10) {
                        overlays.push({
                            element: el,
                            zIndex: style.zIndex,
                            position: style.position
                        });
                    }
                }
            });
            
            console.log('Detected high z-index elements:', overlays);
            return overlays;
        }

        // Run detection on load
        window.onload = () => {
            const overlays = detectOverlays();
            if (overlays.length > 0) {
                handleClick(`Detected ${overlays.length} potential overlay elements`);
            }
        };
    </script>
</body>
</html>
