/* Fix for button click issues */

/* Ensure all interactive elements have proper pointer events */
button, 
[role="button"],
.cursor-pointer,
input[type="button"],
input[type="submit"],
input[type="reset"] {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Remove any global pointer-events none that might be blocking clicks */
* {
  pointer-events: auto;
}

/* Ensure proper z-index for interactive elements */
button,
[role="button"],
.cursor-pointer {
  position: relative;
  z-index: 10;
}

/* Fix for any potential overlay issues */
body::before,
body::after,
html::before,
html::after {
  pointer-events: none;
}

/* Debug helper - adds a red border to all clickable elements */
.debug-clickable button,
.debug-clickable [role="button"],
.debug-clickable .cursor-pointer {
  border: 2px solid red !important;
}

/* Ensure React root doesn't block events */
#root {
  pointer-events: auto;
}

/* Fix for any modal or overlay issues */
.modal-overlay,
.backdrop,
[data-backdrop] {
  pointer-events: auto;
}

.modal-overlay > *,
.backdrop > * {
  pointer-events: auto;
}
